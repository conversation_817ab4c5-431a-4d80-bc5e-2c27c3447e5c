import 'package:flutter/material.dart';

/// 打字机效果文本组件
/// 
/// 实现逐字符显示动画效果，模拟AI回复的打字过程
class TypewriterText extends StatefulWidget {
  /// 要显示的完整文本
  final String text;
  
  /// 文本样式
  final TextStyle? style;
  
  /// 打字速度（每个字符的显示间隔，毫秒）
  final Duration speed;
  
  /// 动画完成回调
  final VoidCallback? onComplete;
  
  /// 是否自动开始动画
  final bool autoStart;
  
  /// 文本对齐方式
  final TextAlign? textAlign;
  
  /// 最大行数
  final int? maxLines;
  
  /// 文本溢出处理
  final TextOverflow? overflow;

  const TypewriterText({
    Key? key,
    required this.text,
    this.style,
    this.speed = const Duration(milliseconds: 50),
    this.onComplete,
    this.autoStart = true,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : super(key: key);

  @override
  State<TypewriterText> createState() => _TypewriterTextState();
}

class _TypewriterTextState extends State<TypewriterText>
    with SingleTickerProviderStateMixin {
  
  /// 当前显示的文本
  String _displayedText = '';
  
  /// 当前字符索引
  int _currentIndex = 0;
  
  /// 动画控制器
  late AnimationController _controller;
  
  /// 动画
  late Animation<int> _animation;
  
  /// 是否动画完成
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
    
    if (widget.autoStart) {
      _startAnimation();
    }
  }

  @override
  void didUpdateWidget(TypewriterText oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果文本内容改变，重新开始动画
    if (oldWidget.text != widget.text) {
      _resetAnimation();
      if (widget.autoStart) {
        _startAnimation();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimation() {
    final duration = Duration(
      milliseconds: widget.text.length * widget.speed.inMilliseconds,
    );
    
    _controller = AnimationController(
      duration: duration,
      vsync: this,
    );

    _animation = IntTween(
      begin: 0,
      end: widget.text.length,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _animation.addListener(_updateText);
    _animation.addStatusListener(_onAnimationStatusChanged);
  }

  /// 更新显示的文本
  void _updateText() {
    if (!mounted) return;
    
    final newIndex = _animation.value;
    if (newIndex != _currentIndex) {
      setState(() {
        _currentIndex = newIndex;
        _displayedText = widget.text.substring(0, _currentIndex);
      });
    }
  }

  /// 动画状态改变回调
  void _onAnimationStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isCompleted) {
      _isCompleted = true;
      widget.onComplete?.call();
    }
  }

  /// 开始动画
  void _startAnimation() {
    if (!mounted) return;
    _controller.forward();
  }

  /// 重置动画
  void _resetAnimation() {
    _controller.reset();
    _currentIndex = 0;
    _displayedText = '';
    _isCompleted = false;
  }

  /// 停止动画并显示完整文本
  void _completeImmediately() {
    _controller.stop();
    setState(() {
      _currentIndex = widget.text.length;
      _displayedText = widget.text;
      _isCompleted = true;
    });
    widget.onComplete?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // 点击可以立即显示完整文本
      onTap: _isCompleted ? null : _completeImmediately,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Text(
            _displayedText,
            style: widget.style,
            textAlign: widget.textAlign,
            maxLines: widget.maxLines,
            overflow: widget.overflow,
          );
        },
      ),
    );
  }
}

/// 带光标的打字机效果文本组件
class TypewriterTextWithCursor extends StatefulWidget {
  /// 要显示的完整文本
  final String text;
  
  /// 文本样式
  final TextStyle? style;
  
  /// 打字速度
  final Duration speed;
  
  /// 光标样式
  final TextStyle? cursorStyle;
  
  /// 光标字符
  final String cursor;
  
  /// 光标闪烁速度
  final Duration cursorBlinkSpeed;
  
  /// 动画完成回调
  final VoidCallback? onComplete;
  
  /// 是否自动开始
  final bool autoStart;

  const TypewriterTextWithCursor({
    Key? key,
    required this.text,
    this.style,
    this.speed = const Duration(milliseconds: 50),
    this.cursorStyle,
    this.cursor = '|',
    this.cursorBlinkSpeed = const Duration(milliseconds: 500),
    this.onComplete,
    this.autoStart = true,
  }) : super(key: key);

  @override
  State<TypewriterTextWithCursor> createState() => _TypewriterTextWithCursorState();
}

class _TypewriterTextWithCursorState extends State<TypewriterTextWithCursor>
    with TickerProviderStateMixin {
  
  late AnimationController _typeController;
  late AnimationController _cursorController;
  late Animation<int> _typeAnimation;
  late Animation<double> _cursorAnimation;
  
  String _displayedText = '';
  bool _showCursor = true;
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    
    if (widget.autoStart) {
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _typeController.dispose();
    _cursorController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    // 打字动画
    _typeController = AnimationController(
      duration: Duration(
        milliseconds: widget.text.length * widget.speed.inMilliseconds,
      ),
      vsync: this,
    );

    _typeAnimation = IntTween(
      begin: 0,
      end: widget.text.length,
    ).animate(CurvedAnimation(
      parent: _typeController,
      curve: Curves.linear,
    ));

    // 光标闪烁动画
    _cursorController = AnimationController(
      duration: widget.cursorBlinkSpeed,
      vsync: this,
    );

    _cursorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_cursorController);

    _typeAnimation.addListener(_updateText);
    _typeAnimation.addStatusListener(_onTypeAnimationStatusChanged);
    _cursorAnimation.addListener(_updateCursor);
  }

  void _updateText() {
    if (!mounted) return;
    
    setState(() {
      _displayedText = widget.text.substring(0, _typeAnimation.value);
    });
  }

  void _updateCursor() {
    if (!mounted) return;
    
    setState(() {
      _showCursor = _cursorAnimation.value > 0.5;
    });
  }

  void _onTypeAnimationStatusChanged(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isCompleted) {
      _isCompleted = true;
      _cursorController.stop();
      setState(() {
        _showCursor = false;
      });
      widget.onComplete?.call();
    }
  }

  void _startAnimations() {
    _typeController.forward();
    _cursorController.repeat();
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: _displayedText,
            style: widget.style ?? DefaultTextStyle.of(context).style,
          ),
          if (_showCursor && !_isCompleted)
            TextSpan(
              text: widget.cursor,
              style: widget.cursorStyle ?? 
                     widget.style ?? 
                     DefaultTextStyle.of(context).style,
            ),
        ],
      ),
    );
  }
}
