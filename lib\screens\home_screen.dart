import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/device_provider.dart';
import '../screens/health_screen.dart';
import '../screens/device_control_screen.dart';
import '../screens/device_manage_screen.dart';
import '../screens/profile_screen.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  final List<Widget> _children = [
    HealthScreen(),
    DeviceControlScreen(),
    DeviceManageScreen(),
    ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);
      deviceProvider.initDevices(true);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 只在首次加载或有新参数时更新索引
    final args = ModalRoute.of(context)?.settings.arguments as int?;
    if (args != null && args != _currentIndex) {
      setState(() {
        _currentIndex = args;
      });
    }
  }

  void onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 加载设备的属性
    return Scaffold(
      body: Stack(
        children: [
          _children[_currentIndex],
          // 凸出的AI助手按钮
          _buildFloatingAIButton(),
        ],
      ),
      bottomNavigationBar: _buildCustomBottomNavigationBar(),
    );
  }

  /// 构建凸出的AI助手按钮
  Widget _buildFloatingAIButton() {
    return Positioned(
      bottom: 25, // 距离底部的距离
      left: MediaQuery.of(context).size.width / 2 - 30, // 居中
      child: GestureDetector(
        onTap: () => Navigator.pushNamed(context, '/chatbot'),
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.orange.shade400,
                Colors.orange.shade600,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.4),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
          ),
          child: const Icon(
            Icons.smart_toy,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }

  /// 构建自定义底部导航栏
  Widget _buildCustomBottomNavigationBar() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(0, Icons.monitor_heart_rounded, '健康'),
          _buildNavItem(1, Icons.security, '守护'),
          // 中间空位给凸出的AI按钮
          const SizedBox(width: 60),
          _buildNavItem(2, Icons.watch, '设备'),
          _buildNavItem(3, Icons.person, '我的'),
        ],
      ),
    );
  }

  /// 构建导航项
  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = _currentIndex == index;
    return GestureDetector(
      onTap: () => onTabTapped(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: isSelected
                  ? const Color.fromARGB(255, 243, 117, 33)
                  : Colors.grey[600],
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected
                    ? const Color.fromARGB(255, 243, 117, 33)
                    : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
