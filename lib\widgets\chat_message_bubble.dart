import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chat_message.dart';
import 'typewriter_text.dart';

/// 聊天消息气泡组件
/// 
/// 区分用户消息和AI消息的显示样式
class ChatMessageBubble extends StatelessWidget {
  /// 聊天消息
  final ChatMessage message;
  
  /// 消息状态更新回调（用于打字机效果完成后更新状态）
  final Function(String messageId, MessageStatus status)? onStatusUpdate;
  
  /// 重发消息回调
  final Function(ChatMessage message)? onResend;
  
  /// 是否显示时间戳
  final bool showTimestamp;
  
  /// 是否显示头像
  final bool showAvatar;

  const ChatMessageBubble({
    Key? key,
    required this.message,
    this.onStatusUpdate,
    this.onResend,
    this.showTimestamp = true,
    this.showAvatar = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Column(
        crossAxisAlignment: message.isUser 
            ? CrossAxisAlignment.end 
            : CrossAxisAlignment.start,
        children: [
          // 时间戳
          if (showTimestamp) _buildTimestamp(),
          
          // 消息内容
          Row(
            mainAxisAlignment: message.isUser 
                ? MainAxisAlignment.end 
                : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // AI头像（左侧）
              if (!message.isUser && showAvatar) _buildAvatar(),
              
              // 消息气泡
              Flexible(child: _buildMessageBubble(context)),
              
              // 用户头像（右侧）
              if (message.isUser && showAvatar) _buildAvatar(),
            ],
          ),
          
          // 消息状态指示器
          if (message.isUser) _buildStatusIndicator(context),
        ],
      ),
    );
  }

  /// 构建时间戳
  Widget _buildTimestamp() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        DateFormat('HH:mm').format(message.timestamp),
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar() {
    return Container(
      margin: EdgeInsets.only(
        left: message.isUser ? 8 : 0,
        right: message.isUser ? 0 : 8,
      ),
      child: CircleAvatar(
        radius: 16,
        backgroundColor: message.isUser 
            ? Colors.blue[100] 
            : Colors.orange[100],
        child: Icon(
          message.isUser ? Icons.person : Icons.smart_toy,
          size: 18,
          color: message.isUser 
              ? Colors.blue[700] 
              : Colors.orange[700],
        ),
      ),
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.7,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getBubbleColor(),
        borderRadius: _getBubbleBorderRadius(),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildMessageContent(),
    );
  }

  /// 获取气泡颜色
  Color _getBubbleColor() {
    if (message.isUser) {
      return Colors.blue[500]!;
    } else {
      return Colors.grey[100]!;
    }
  }

  /// 获取气泡圆角
  BorderRadius _getBubbleBorderRadius() {
    const radius = Radius.circular(16);
    const smallRadius = Radius.circular(4);
    
    if (message.isUser) {
      return const BorderRadius.only(
        topLeft: radius,
        topRight: radius,
        bottomLeft: radius,
        bottomRight: smallRadius,
      );
    } else {
      return const BorderRadius.only(
        topLeft: radius,
        topRight: radius,
        bottomLeft: smallRadius,
        bottomRight: radius,
      );
    }
  }

  /// 构建消息内容
  Widget _buildMessageContent() {
    final textStyle = TextStyle(
      fontSize: 16,
      color: message.isUser ? Colors.white : Colors.black87,
      height: 1.4,
    );

    // AI消息且状态为typing时使用打字机效果
    if (!message.isUser && message.status == MessageStatus.typing) {
      return TypewriterText(
        text: message.content,
        style: textStyle,
        speed: const Duration(milliseconds: 30),
        onComplete: () {
          // 打字机效果完成后更新消息状态
          onStatusUpdate?.call(message.id, MessageStatus.sent);
        },
      );
    }

    // 普通文本显示
    return Text(
      message.content,
      style: textStyle,
    );
  }

  /// 构建状态指示器（仅用户消息）
  Widget _buildStatusIndicator(BuildContext context) {
    if (!message.isUser) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(top: 4, right: showAvatar ? 40 : 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildStatusIcon(),
          if (message.status == MessageStatus.failed) ...[
            const SizedBox(width: 8),
            _buildResendButton(context),
          ],
        ],
      ),
    );
  }

  /// 构建状态图标
  Widget _buildStatusIcon() {
    switch (message.status) {
      case MessageStatus.sending:
        return SizedBox(
          width: 12,
          height: 12,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
          ),
        );
      
      case MessageStatus.sent:
        return Icon(
          Icons.check,
          size: 16,
          color: Colors.grey[600],
        );
      
      case MessageStatus.failed:
        return Icon(
          Icons.error_outline,
          size: 16,
          color: Colors.red[400],
        );
      
      case MessageStatus.typing:
        return const SizedBox.shrink();
    }
  }

  /// 构建重发按钮
  Widget _buildResendButton(BuildContext context) {
    return GestureDetector(
      onTap: () => onResend?.call(message),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.refresh,
              size: 14,
              color: Colors.red[600],
            ),
            const SizedBox(width: 4),
            Text(
              '重发',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 系统消息气泡（用于显示系统提示信息）
class SystemMessageBubble extends StatelessWidget {
  final String message;
  final IconData? icon;
  final Color? color;

  const SystemMessageBubble({
    Key? key,
    required this.message,
    this.icon,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: (color ?? Colors.grey[300])!.withOpacity(0.3),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 16,
                  color: color ?? Colors.grey[600],
                ),
                const SizedBox(width: 8),
              ],
              Text(
                message,
                style: TextStyle(
                  fontSize: 14,
                  color: color ?? Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
