import 'dart:math';
import '../services/api_service.dart';
import '../utils/app_logger.dart';

/// AI聊天服务
/// 遵循现有ApiService风格，提供AI对话功能
class ChatbotService {
  final ApiService _apiService;

  ChatbotService(this._apiService);

  /// 发送消息给AI并获取回复
  /// 目前使用模拟数据，后续可替换为真实API
  Future<String> sendMessage(String message) async {
    try {
      AppLogger.info('发送消息给AI: $message');

      // 模拟网络延迟
      await Future.delayed(
          Duration(milliseconds: 800 + Random().nextInt(1200)));

      // 模拟AI回复
      final aiResponse = _generateAIResponse(message);

      AppLogger.info('AI回复: $aiResponse');
      return aiResponse;
    } catch (e, stackTrace) {
      AppLogger.error('AI聊天服务错误：$e', error: e, stackTrace: stackTrace);
      throw Exception('AI服务暂时不可用，请稍后重试');
    }
  }

  /// 生成模拟AI回复
  String _generateAIResponse(String userMessage) {
    final message = userMessage.toLowerCase().trim();

    // 问候语回复
    if (_containsAny(message, ['你好', 'hi', 'hello', '您好'])) {
      return _getRandomResponse([
        '你好！我是您的AI助手，很高兴为您服务！有什么可以帮助您的吗？',
        '您好！我可以为您提供宠物护理建议、健康咨询等服务。',
        '嗨！欢迎使用AI助手，我可以帮您解答关于宠物的各种问题。',
      ]);
    }

    // 宠物健康相关
    if (_containsAny(message, ['健康', '生病', '症状', '医生', '看病'])) {
      return _getRandomResponse([
        '关于宠物健康问题，建议您：\n1. 观察宠物的精神状态和食欲\n2. 记录异常症状的时间和表现\n3. 如症状持续，请及时就医\n\n需要我提供更具体的建议吗？',
        '宠物的健康很重要！请告诉我具体的症状，我可以给您一些初步建议。但请记住，严重情况下还是要咨询专业兽医。',
        '我可以帮您分析宠物的健康状况。请描述一下您观察到的具体症状，比如食欲、精神状态、行为变化等。',
      ]);
    }

    // 喂养相关
    if (_containsAny(message, ['喂养', '食物', '吃', '饮食', '营养'])) {
      return _getRandomResponse([
        '关于宠物喂养，这里有一些建议：\n• 定时定量喂食\n• 选择适合年龄的优质食物\n• 保证充足的饮水\n• 避免喂食人类食物\n\n您的宠物多大了？我可以提供更具体的建议。',
        '良好的饮食习惯对宠物很重要！请告诉我您宠物的品种和年龄，我可以推荐合适的喂养方案。',
        '宠物营养需要均衡搭配。不同年龄段的宠物需求不同，您可以详细描述一下您的宠物情况吗？',
      ]);
    }

    // 训练相关
    if (_containsAny(message, ['训练', '教育', '行为', '听话', '乖'])) {
      return _getRandomResponse([
        '宠物训练需要耐心和一致性：\n• 使用正向强化方法\n• 保持训练的一致性\n• 及时奖励正确行为\n• 避免体罚\n\n您想训练宠物什么技能呢？',
        '训练宠物是个循序渐进的过程。建议从基础指令开始，比如"坐下"、"等待"等。您的宠物现在有什么行为问题吗？',
        '良好的训练可以增进您和宠物的关系。请告诉我您遇到的具体问题，我可以提供针对性的训练建议。',
      ]);
    }

    // 设备相关
    if (_containsAny(message, ['设备', '项圈', '监测', '数据'])) {
      return _getRandomResponse([
        '智能宠物设备可以帮助您更好地了解宠物状态：\n• 实时监测健康数据\n• 追踪活动轨迹\n• 分析行为模式\n\n您对哪个功能最感兴趣？',
        '通过设备监测，我们可以及早发现宠物的健康问题。建议定期查看监测数据，关注异常变化。',
        '设备数据可以为宠物护理提供科学依据。您可以结合监测结果调整日常护理方案。',
      ]);
    }

    // 感谢语
    if (_containsAny(message, ['谢谢', '感谢', 'thanks', '谢了'])) {
      return _getRandomResponse([
        '不客气！很高兴能帮助到您。如果还有其他问题，随时可以问我。',
        '您太客气了！为您和您的宠物服务是我的荣幸。',
        '不用谢！希望我的建议对您有帮助。还有什么想了解的吗？',
      ]);
    }

    // 默认回复
    return _getRandomResponse([
      '这是一个很有趣的问题！能告诉我更多详细信息吗？这样我可以给您更准确的建议。',
      '我理解您的关注。关于这个问题，建议您提供更多背景信息，比如宠物的品种、年龄等。',
      '感谢您的提问！为了给您最合适的建议，请详细描述一下具体情况。',
      '这个问题很重要！请告诉我更多细节，我会尽力为您提供专业的建议。',
      '我很乐意帮助您！能否提供更多相关信息？比如您的宠物现在的状况如何？',
    ]);
  }

  /// 检查消息是否包含指定关键词
  bool _containsAny(String message, List<String> keywords) {
    return keywords.any((keyword) => message.contains(keyword));
  }

  /// 随机选择一个回复
  String _getRandomResponse(List<String> responses) {
    return responses[Random().nextInt(responses.length)];
  }
}
