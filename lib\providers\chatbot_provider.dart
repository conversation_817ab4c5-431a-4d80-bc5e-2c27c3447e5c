import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';
import '../services/chatbot_service.dart';
import '../utils/app_logger.dart';
import '../utils/user_context.dart';

/// AI聊天状态管理
class ChatbotProvider with ChangeNotifier {
  final ChatbotService _chatbotService;

  ChatbotProvider(this._chatbotService);

  /// 消息列表
  List<ChatMessage> _messages = [];
  List<ChatMessage> get messages => List.unmodifiable(_messages);

  /// 是否正在发送消息
  bool _isSending = false;
  bool get isSending => _isSending;

  /// AI是否正在输入
  bool _isAITyping = false;
  bool get isAITyping => _isAITyping;

  /// 存储键名（支持用户数据隔离）
  String get _storageKey => UserContext.instance.getUserKey('chatbot_messages');

  /// 初始化，加载历史消息
  Future<void> init() async {
    try {
      await _loadMessages();
      AppLogger.info('ChatbotProvider初始化完成，加载了${_messages.length}条消息');
    } catch (e, stackTrace) {
      AppLogger.error('ChatbotProvider初始化失败：$e',
          error: e, stackTrace: stackTrace);
    }
  }

  /// 发送消息
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || _isSending) return;

    try {
      _isSending = true;
      notifyListeners();

      // 添加用户消息
      final userMessage = ChatMessage.user(content: content.trim());
      _messages.add(userMessage);
      notifyListeners();

      // 保存消息到本地
      await _saveMessages();

      // 设置AI正在输入状态
      _isAITyping = true;
      notifyListeners();

      // 发送给AI并获取回复
      final aiResponse = await _chatbotService.sendMessage(content);

      // 添加AI回复消息
      final aiMessage = ChatMessage.ai(
        content: aiResponse,
        status: MessageStatus.typing, // 初始状态为typing，用于打字机效果
      );
      _messages.add(aiMessage);

      _isAITyping = false;
      notifyListeners();

      // 保存消息到本地
      await _saveMessages();
    } catch (e, stackTrace) {
      AppLogger.error('发送消息失败：$e', error: e, stackTrace: stackTrace);

      // 更新用户消息状态为失败
      if (_messages.isNotEmpty && _messages.last.isUser) {
        final lastIndex = _messages.length - 1;
        _messages[lastIndex] = _messages[lastIndex].copyWith(
          status: MessageStatus.failed,
        );
      }

      _isAITyping = false;
      notifyListeners();

      // 可以在这里显示错误提示
      rethrow;
    } finally {
      _isSending = false;
      notifyListeners();
    }
  }

  /// 重新发送失败的消息
  Future<void> resendMessage(ChatMessage message) async {
    if (!message.isUser || message.status != MessageStatus.failed) return;

    // 移除失败的消息
    _messages.removeWhere((msg) => msg.id == message.id);
    notifyListeners();

    // 重新发送
    await sendMessage(message.content);
  }

  /// 更新AI消息状态（用于打字机效果完成后）
  void updateMessageStatus(String messageId, MessageStatus status) {
    final index = _messages.indexWhere((msg) => msg.id == messageId);
    if (index != -1) {
      _messages[index] = _messages[index].copyWith(status: status);
      notifyListeners();
    }
  }

  /// 新建对话（清空当前消息）
  Future<void> createNewConversation() async {
    try {
      _messages.clear();
      await _saveMessages();
      notifyListeners();
      AppLogger.info('已创建新对话');
    } catch (e, stackTrace) {
      AppLogger.error('创建新对话失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  /// 删除指定消息
  Future<void> deleteMessage(String messageId) async {
    try {
      _messages.removeWhere((msg) => msg.id == messageId);
      await _saveMessages();
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('删除消息失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  /// 清空所有消息
  Future<void> clearAllMessages() async {
    await createNewConversation();
  }

  /// 从本地存储加载消息
  Future<void> _loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getString(_storageKey);

      if (messagesJson != null) {
        final List<dynamic> messagesList = jsonDecode(messagesJson);
        _messages =
            messagesList.map((json) => ChatMessage.fromJson(json)).toList();
      }
    } catch (e, stackTrace) {
      AppLogger.error('加载消息失败：$e', error: e, stackTrace: stackTrace);
      _messages = []; // 加载失败时使用空列表
    }
  }

  /// 保存消息到本地存储
  Future<void> _saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = jsonEncode(
        _messages.map((msg) => msg.toJson()).toList(),
      );
      await prefs.setString(_storageKey, messagesJson);
    } catch (e, stackTrace) {
      AppLogger.error('保存消息失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  /// 获取消息数量
  int get messageCount => _messages.length;

  /// 是否有消息
  bool get hasMessages => _messages.isNotEmpty;

  /// 获取最后一条消息
  ChatMessage? get lastMessage => _messages.isNotEmpty ? _messages.last : null;
}
